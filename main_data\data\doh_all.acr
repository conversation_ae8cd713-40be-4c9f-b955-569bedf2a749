%SECTION%1%;Aircraft;
%FIELDS%;aircraft_id;type;start_node;end_node;start_time;end_time;appearance_time;speed_min;speed_ideal;speed_max;sid_route;take-off_speed_group;weight_class;speed_profile;
;0;departure;A2;N8;[61320,61320,61320];[61320,61320,61320];0;1.0;1.0;1.0;1;1;3;1;
;1;departure;E7;N15;[61740,61740,61740];[61740,61740,61740];0;1.0;1.0;1.0;1;1;2;1;
;2;departure;A4;N8;[61980,61980,61980];[61980,61980,61980];0;1.0;1.0;1.0;1;1;3;1;
;3;departure;A16;N8;[62100,62100,62100];[62100,62100,62100];0;1.0;1.0;1.0;1;1;3;1;
;4;departure;E12;N15;[62160,62160,62160];[62160,62160,62160];0;1.0;1.0;1.0;1;1;2;1;
;5;departure;E5;N15;[62520,62520,62520];[62520,62520,62520];0;1.0;1.0;1.0;1;1;3;1;
;6;departure;A18;N8;[62700,62700,62700];[62700,62700,62700];0;1.0;1.0;1.0;1;1;3;1;
;7;departure;E5;N15;[62940,62940,62940];[62940,62940,62940];0;1.0;1.0;1.0;1;1;2;1;
;8;departure;A3;N8;[63060,63060,63060];[63060,63060,63060];0;1.0;1.0;1.0;1;1;3;1;
;9;departure;E9;N15;[63240,63240,63240];[63240,63240,63240];0;1.0;1.0;1.0;1;1;2;1;
;10;departure;E14;N15;[63360,63360,63360];[63360,63360,63360];0;1.0;1.0;1.0;1;1;3;1;
;11;departure;E13;N15;[63660,63660,63660];[63660,63660,63660];0;1.0;1.0;1.0;1;1;2;1;
;12;departure;E8;N15;[63660,63660,63660];[63660,63660,63660];0;1.0;1.0;1.0;1;1;2;1;
;13;departure;A12;N8;[63720,63720,63720];[63720,63720,63720];0;1.0;1.0;1.0;1;1;3;1;
;14;arrival;N38;W6;[63840,63840,63840];[63840,63840,63840];0;1.0;1.0;1.0;1;1;3;1;
;15;departure;E12;N15;[63900,63900,63900];[63900,63900,63900];0;1.0;1.0;1.0;1;1;2;1;
;16;departure;E6;N15;[63900,63900,63900];[63900,63900,63900];0;1.0;1.0;1.0;1;1;2;1;
;17;departure;E11;N15;[64080,64080,64080];[64080,64080,64080];0;1.0;1.0;1.0;1;1;3;1;
;18;arrival;N38;W7;[64080,64080,64080];[64080,64080,64080];0;1.0;1.0;1.0;1;1;3;1;
;19;arrival;N38;A10;[64260,64260,64260];[64260,64260,64260];0;1.0;1.0;1.0;1;1;2;1;
;20;departure;E10;N15;[64560,64560,64560];[64560,64560,64560];0;1.0;1.0;1.0;1;1;2;1;
;21;departure;A9;N8;[64620,64620,64620];[64620,64620,64620];0;1.0;1.0;1.0;1;1;2;1;
;22;departure;W2;N8;[64680,64680,64680];[64680,64680,64680];0;1.0;1.0;1.0;1;1;3;1;
;23;departure;E2;N15;[64740,64740,64740];[64740,64740,64740];0;1.0;1.0;1.0;1;1;3;1;
;24;departure;A11;N8;[65040,65040,65040];[65040,65040,65040];0;1.0;1.0;1.0;1;1;2;1;
;25;departure;A15;N8;[65160,65160,65160];[65160,65160,65160];0;1.0;1.0;1.0;1;1;3;1;
;26;departure;E26;N15;[65460,65460,65460];[65460,65460,65460];0;1.0;1.0;1.0;1;1;3;1;
;27;departure;E9;N15;[65460,65460,65460];[65460,65460,65460];0;1.0;1.0;1.0;1;1;2;1;
;28;departure;W3;N8;[65520,65520,65520];[65520,65520,65520];0;1.0;1.0;1.0;1;1;2;1;
;29;arrival;N38;A15;[65640,65640,65640];[65640,65640,65640];0;1.0;1.0;1.0;1;1;3;1;
;30;departure;E6;N15;[65760,65760,65760];[65760,65760,65760];0;1.0;1.0;1.0;1;1;2;1;
;31;arrival;N38;W3;[65880,65880,65880];[65880,65880,65880];0;1.0;1.0;1.0;1;1;3;1;
;32;arrival;N38;A9;[66360,66360,66360];[66360,66360,66360];0;1.0;1.0;1.0;1;1;2;1;
;33;departure;A13;N8;[66780,66780,66780];[66780,66780,66780];0;1.0;1.0;1.0;1;1;3;1;
;34;arrival;N38;A11;[66960,66960,66960];[66960,66960,66960];0;1.0;1.0;1.0;1;1;2;1;
;35;departure;A10;N8;[67020,67020,67020];[67020,67020,67020];0;1.0;1.0;1.0;1;1;2;1;
;36;arrival;N38;E1;[67260,67260,67260];[67260,67260,67260];0;1.0;1.0;1.0;1;1;2;1;
;37;departure;A11;N8;[67440,67440,67440];[67440,67440,67440];0;1.0;1.0;1.0;1;1;2;1;
;38;arrival;N38;A17;[67680,67680,67680];[67680,67680,67680];0;1.0;1.0;1.0;1;1;3;1;
;39;arrival;N38;W2;[67800,67800,67800];[67800,67800,67800];0;1.0;1.0;1.0;1;1;3;1;
;40;arrival;N38;A12;[68160,68160,68160];[68160,68160,68160];0;1.0;1.0;1.0;1;1;2;1;
;41;arrival;N38;E20;[68520,68520,68520];[68520,68520,68520];0;1.0;1.0;1.0;1;1;3;1;
;42;arrival;N38;E1;[68700,68700,68700];[68700,68700,68700];0;1.0;1.0;1.0;1;1;2;1;
;43;arrival;N38;A15;[68880,68880,68880];[68880,68880,68880];0;1.0;1.0;1.0;1;1;3;1;
;44;arrival;N38;E22;[69060,69060,69060];[69060,69060,69060];0;1.0;1.0;1.0;1;1;3;1;
;45;arrival;N38;W1;[69300,69300,69300];[69300,69300,69300];0;1.0;1.0;1.0;1;1;2;1;
;46;arrival;N38;A11;[69780,69780,69780];[69780,69780,69780];0;1.0;1.0;1.0;1;1;3;1;
;47;arrival;N38;E25;[69900,69900,69900];[69900,69900,69900];0;1.0;1.0;1.0;1;1;3;1;
;48;departure;A12;N8;[69960,69960,69960];[69960,69960,69960];0;1.0;1.0;1.0;1;1;2;1;
;49;arrival;N38;W6;[70140,70140,70140];[70140,70140,70140];0;1.0;1.0;1.0;1;1;2;1;
;50;arrival;N38;E23;[70380,70380,70380];[70380,70380,70380];0;1.0;1.0;1.0;1;1;3;1;
;51;arrival;N38;E12;[70560,70560,70560];[70560,70560,70560];0;1.0;1.0;1.0;1;1;3;1;
;52;arrival;N38;E6;[70717,70717,70717];[70717,70717,70717];0;1.0;1.0;1.0;1;1;2;1;
;53;arrival;N38;E6;[70786,70786,70786];[70786,70786,70786];0;1.0;1.0;1.0;1;1;2;1;
;54;departure;A11;N8;[70860,70860,70860];[70860,70860,70860];0;1.0;1.0;1.0;1;1;2;1;
;55;arrival;N38;E4;[70920,70920,70920];[70920,70920,70920];0;1.0;1.0;1.0;1;1;3;1;
;56;departure;W3;N8;[71040,71040,71040];[71040,71040,71040];0;1.0;1.0;1.0;1;1;2;1;
;57;arrival;N38;E13;[71100,71100,71100];[71100,71100,71100];0;1.0;1.0;1.0;1;1;3;1;
;58;arrival;N38;E12;[71280,71280,71280];[71280,71280,71280];0;1.0;1.0;1.0;1;1;2;1;
;59;arrival;N38;A11;[71349,71349,71349];[71349,71349,71349];0;1.0;1.0;1.0;1;1;2;1;
;60;departure;A10;N8;[71460,71460,71460];[71460,71460,71460];0;1.0;1.0;1.0;1;1;3;1;
;61;departure;W2;N8;[71520,71520,71520];[71520,71520,71520];0;1.0;1.0;1.0;1;1;3;1;
;62;departure;A12;N8;[71580,71580,71580];[71580,71580,71580];0;1.0;1.0;1.0;1;1;2;1;
;63;arrival;N38;A14;[71640,71640,71640];[71640,71640,71640];0;1.0;1.0;1.0;1;1;3;1;
;64;arrival;N38;E6;[71940,71940,71940];[71940,71940,71940];0;1.0;1.0;1.0;1;1;2;1;
;65;arrival;N38;E11;[72120,72120,72120];[72120,72120,72120];0;1.0;1.0;1.0;1;1;3;1;
;66;arrival;N38;E5;[72660,72660,72660];[72660,72660,72660];0;1.0;1.0;1.0;1;1;3;1;
;67;arrival;N42;A1;[73080,73080,73080];[73080,73080,73080];0;1.0;1.0;1.0;1;1;3;1;
;68;departure;A15;N8;[73260,73260,73260];[73260,73260,73260];0;1.0;1.0;1.0;1;1;3;1;
;69;arrival;N38;W3;[73320,73320,73320];[73320,73320,73320];0;1.0;1.0;1.0;1;1;2;1;
;70;arrival;N38;A18;[73440,73440,73440];[73440,73440,73440];0;1.0;1.0;1.0;1;1;3;1;
;71;arrival;N38;E23;[73800,73800,73800];[73800,73800,73800];0;1.0;1.0;1.0;1;1;3;1;
;72;departure;W2;N8;[73860,73860,73860];[73860,73860,73860];0;1.0;1.0;1.0;1;1;2;1;
;73;arrival;N38;E10;[73920,73920,73920];[73920,73920,73920];0;1.0;1.0;1.0;1;1;3;1;
;74;departure;E1;N15;[74280,74280,74280];[74280,74280,74280];0;1.0;1.0;1.0;1;1;2;1;
;75;arrival;N38;E4;[74520,74520,74520];[74520,74520,74520];0;1.0;1.0;1.0;1;1;3;1;
;76;departure;A3;N8;[74580,74580,74580];[74580,74580,74580];0;1.0;1.0;1.0;1;1;3;1;
;77;arrival;N38;E20;[74580,74580,74580];[74580,74580,74580];0;1.0;1.0;1.0;1;1;2;1;
;78;departure;E5;N15;[74580,74580,74580];[74580,74580,74580];0;1.0;1.0;1.0;1;1;3;1;
;79;departure;A17;N8;[74580,74580,74580];[74580,74580,74580];0;1.0;1.0;1.0;1;1;3;1;
;80;arrival;N38;W6;[74640,74640,74640];[74640,74640,74640];0;1.0;1.0;1.0;1;1;2;1;
;81;arrival;N38;E20;[74709,74709,74709];[74709,74709,74709];0;1.0;1.0;1.0;1;1;3;1;
;82;arrival;N38;E17;[74866,74866,74866];[74866,74866,74866];0;1.0;1.0;1.0;1;1;2;1;
;84;departure;A11;N8;[74880,74880,74880];[74880,74880,74880];0;1.0;1.0;1.0;1;1;2;1;
;83;arrival;N38;E16;[74935,74935,74935];[74935,74935,74935];0;1.0;1.0;1.0;1;1;2;1;
;85;arrival;N38;E9;[75004,75004,75004];[75004,75004,75004];0;1.0;1.0;1.0;1;1;2;1;
;86;arrival;N38;E5;[75073,75073,75073];[75073,75073,75073];0;1.0;1.0;1.0;1;1;2;1;
;88;departure;E14;N15;[75120,75120,75120];[75120,75120,75120];0;1.0;1.0;1.0;1;1;3;1;
;87;arrival;N38;E2;[75142,75142,75142];[75142,75142,75142];0;1.0;1.0;1.0;1;1;2;1;
;89;arrival;N42;A1;[75202,75202,75202];[75202,75202,75202];0;1.0;1.0;1.0;1;1;3;1;
;90;arrival;N38;A16;[75298,75298,75298];[75298,75298,75298];0;1.0;1.0;1.0;1;1;3;1;
;93;departure;W6;N8;[75360,75360,75360];[75360,75360,75360];0;1.0;1.0;1.0;1;1;2;1;
;91;arrival;N38;E6;[75396,75396,75396];[75396,75396,75396];0;1.0;1.0;1.0;1;1;3;1;
;94;departure;W7;N8;[75540,75540,75540];[75540,75540,75540];0;1.0;1.0;1.0;1;1;2;1;
;92;arrival;N38;E10;[75553,75553,75553];[75553,75553,75553];0;1.0;1.0;1.0;1;1;2;1;
;95;arrival;N38;E10;[75660,75660,75660];[75660,75660,75660];0;1.0;1.0;1.0;1;1;2;1;
;96;arrival;N38;A13;[75720,75720,75720];[75720,75720,75720];0;1.0;1.0;1.0;1;1;3;1;
;97;arrival;N38;A12;[75824,75824,75824];[75824,75824,75824];0;1.0;1.0;1.0;1;1;3;1;
;98;arrival;N38;E20;[75920,75920,75920];[75920,75920,75920];0;1.0;1.0;1.0;1;1;3;1;
;99;arrival;N38;E28;[76077,76077,76077];[76077,76077,76077];0;1.0;1.0;1.0;1;1;2;1;
;100;arrival;N38;E5;[76146,76146,76146];[76146,76146,76146];0;1.0;1.0;1.0;1;1;2;1;
;101;arrival;N42;A6;[76206,76206,76206];[76206,76206,76206];0;1.0;1.0;1.0;1;1;3;1;
;102;arrival;N38;E21;[76302,76302,76302];[76302,76302,76302];0;1.0;1.0;1.0;1;1;3;1;
;103;arrival;N38;W7;[76459,76459,76459];[76459,76459,76459];0;1.0;1.0;1.0;1;1;2;1;
;104;arrival;N42;A1;[76522,76522,76522];[76522,76522,76522];0;1.0;1.0;1.0;1;1;3;1;
;105;arrival;N42;A5;[76618,76618,76618];[76618,76618,76618];0;1.0;1.0;1.0;1;1;3;1;
;106;arrival;N42;A3;[76753,76753,76753];[76753,76753,76753];0;1.0;1.0;1.0;1;1;3;1;
;107;arrival;N42;A4;[76884,76884,76884];[76884,76884,76884];0;1.0;1.0;1.0;1;1;3;1;
;108;arrival;N38;A15;[76980,76980,76980];[76980,76980,76980];0;1.0;1.0;1.0;1;1;3;1;
;109;arrival;N38;A10;[77137,77137,77137];[77137,77137,77137];0;1.0;1.0;1.0;1;1;2;1;
;110;arrival;N38;A17;[77197,77197,77197];[77197,77197,77197];0;1.0;1.0;1.0;1;1;3;1;
;112;departure;E20;N15;[77289,77289,77289];[77289,77289,77289];0;1.0;1.0;1.0;1;1;3;1;
;111;arrival;N38;W6;[77354,77354,77354];[77354,77354,77354];0;1.0;1.0;1.0;1;1;2;1;
;113;arrival;N38;E7;[77423,77423,77423];[77423,77423,77423];0;1.0;1.0;1.0;1;1;2;1;
;115;departure;E5;N15;[77444,77444,77444];[77444,77444,77444];0;1.0;1.0;1.0;1;1;2;1;
;114;arrival;N38;W1;[77492,77492,77492];[77492,77492,77492];0;1.0;1.0;1.0;1;1;2;1;
;116;arrival;N38;E5;[77552,77552,77552];[77552,77552,77552];0;1.0;1.0;1.0;1;1;3;1;
;117;arrival;N38;E26;[77698,77698,77698];[77698,77698,77698];0;1.0;1.0;1.0;1;1;3;1;
;118;arrival;N42;A6;[77794,77794,77794];[77794,77794,77794];0;1.0;1.0;1.0;1;1;3;1;
;119;departure;E4;N15;[78120,78120,78120];[78120,78120,78120];0;1.0;1.0;1.0;1;1;3;1;
;120;departure;A2;N8;[78478,78478,78478];[78478,78478,78478];0;1.0;1.0;1.0;1;1;3;1;
;121;departure;W1;N8;[78602,78602,78602];[78602,78602,78602];0;1.0;1.0;1.0;1;1;2;1;
;122;arrival;N38;E14;[78633,78633,78633];[78633,78633,78633];0;1.0;1.0;1.0;1;1;3;1;
;123;departure;E18;N15;[78964,78964,78964];[78964,78964,78964];0;1.0;1.0;1.0;1;1;3;1;
;124;arrival;N42;A4;[78996,78996,78996];[78996,78996,78996];0;1.0;1.0;1.0;1;1;3;1;
;125;departure;A11;N8;[79080,79080,79080];[79080,79080,79080];0;1.0;1.0;1.0;1;1;2;1;
;126;departure;E23;N15;[79102,79102,79102];[79102,79102,79102];0;1.0;1.0;1.0;1;1;3;1;
;127;departure;A18;N8;[79215,79215,79215];[79215,79215,79215];0;1.0;1.0;1.0;1;1;3;1;
;128;arrival;N42;A2;[79218,79218,79218];[79218,79218,79218];0;1.0;1.0;1.0;1;1;3;1;
;129;departure;A10;N8;[79490,79490,79490];[79490,79490,79490];0;1.0;1.0;1.0;1;1;2;1;
;130;departure;E17;N15;[79500,79500,79500];[79500,79500,79500];0;1.0;1.0;1.0;1;1;3;1;
;131;departure;A15;N8;[79518,79518,79518];[79518,79518,79518];0;1.0;1.0;1.0;1;1;3;1;
;132;departure;E2;N15;[79540,79540,79540];[79540,79540,79540];0;1.0;1.0;1.0;1;1;2;1;
;133;departure;A14;N8;[79806,79806,79806];[79806,79806,79806];0;1.0;1.0;1.0;1;1;3;1;
;134;departure;A12;N8;[79838,79838,79838];[79838,79838,79838];0;1.0;1.0;1.0;1;1;2;1;
;135;departure;E9;N15;[79924,79924,79924];[79924,79924,79924];0;1.0;1.0;1.0;1;1;2;1;
;136;departure;A11;N8;[79928,79928,79928];[79928,79928,79928];0;1.0;1.0;1.0;1;1;2;1;
;137;departure;W5;N8;[80100,80100,80100];[80100,80100,80100];0;1.0;1.0;1.0;1;1;2;1;
;138;departure;A10;N8;[80220,80220,80220];[80220,80220,80220];0;1.0;1.0;1.0;1;1;2;1;
;139;departure;E12;N15;[80220,80220,80220];[80220,80220,80220];0;1.0;1.0;1.0;1;1;2;1;
;140;departure;E3;N15;[80256,80256,80256];[80256,80256,80256];0;1.0;1.0;1.0;1;1;3;1;
;141;departure;A12;N8;[80309,80309,80309];[80309,80309,80309];0;1.0;1.0;1.0;1;1;3;1;
;142;departure;A18;N8;[80340,80340,80340];[80340,80340,80340];0;1.0;1.0;1.0;1;1;3;1;
;143;departure;E19;N15;[80400,80400,80400];[80400,80400,80400];0;1.0;1.0;1.0;1;1;3;1;
;144;departure;W7;N8;[80448,80448,80448];[80448,80448,80448];0;1.0;1.0;1.0;1;1;2;1;
;145;departure;E6;N15;[80460,80460,80460];[80460,80460,80460];0;1.0;1.0;1.0;1;1;2;1;
;146;departure;A15;N8;[80506,80506,80506];[80506,80506,80506];0;1.0;1.0;1.0;1;1;3;1;
;147;departure;A16;N8;[80867,80867,80867];[80867,80867,80867];0;1.0;1.0;1.0;1;1;3;1;
;148;departure;E8;N15;[80880,80880,80880];[80880,80880,80880];0;1.0;1.0;1.0;1;1;2;1;
;149;departure;A2;N8;[80938,80938,80938];[80938,80938,80938];0;1.0;1.0;1.0;1;1;3;1;
;150;departure;E23;N15;[81060,81060,81060];[81060,81060,81060];0;1.0;1.0;1.0;1;1;3;1;
;151;departure;A13;N8;[81126,81126,81126];[81126,81126,81126];0;1.0;1.0;1.0;1;1;3;1;
;152;departure;W7;N8;[81516,81516,81516];[81516,81516,81516];0;1.0;1.0;1.0;1;1;2;1;
;153;departure;A5;N8;[81540,81540,81540];[81540,81540,81540];0;1.0;1.0;1.0;1;1;3;1;
;154;departure;E13;N15;[81571,81571,81571];[81571,81571,81571];0;1.0;1.0;1.0;1;1;3;1;
;155;departure;E11;N15;[81780,81780,81780];[81780,81780,81780];0;1.0;1.0;1.0;1;1;2;1;
;156;departure;W7;N8;[81840,81840,81840];[81840,81840,81840];0;1.0;1.0;1.0;1;1;2;1;
;157;departure;W7;N8;[81960,81960,81960];[81960,81960,81960];0;1.0;1.0;1.0;1;1;2;1;
;158;departure;A6;N8;[81986,81986,81986];[81986,81986,81986];0;1.0;1.0;1.0;1;1;3;1;
;159;departure;A13;N8;[82063,82063,82063];[82063,82063,82063];0;1.0;1.0;1.0;1;1;3;1;
;160;departure;A5;N8;[82121,82121,82121];[82121,82121,82121];0;1.0;1.0;1.0;1;1;3;1;
;161;arrival;N38;A17;[82136,82136,82136];[82136,82136,82136];0;1.0;1.0;1.0;1;1;2;1;
;162;departure;E6;N15;[82260,82260,82260];[82260,82260,82260];0;1.0;1.0;1.0;1;1;2;1;
;163;departure;E1;N15;[82260,82260,82260];[82260,82260,82260];0;1.0;1.0;1.0;1;1;2;1;
;164;departure;A12;N8;[82267,82267,82267];[82267,82267,82267];0;1.0;1.0;1.0;1;1;3;1;
;165;departure;A14;N8;[82290,82290,82290];[82290,82290,82290];0;1.0;1.0;1.0;1;1;3;1;
;166;arrival;N38;A18;[82335,82335,82335];[82335,82335,82335];0;1.0;1.0;1.0;1;1;2;1;
;167;departure;E15;N15;[82352,82352,82352];[82352,82352,82352];0;1.0;1.0;1.0;1;1;3;1;
;168;departure;E22;N15;[82380,82380,82380];[82380,82380,82380];0;1.0;1.0;1.0;1;1;3;1;
;169;departure;E28;N15;[82408,82408,82408];[82408,82408,82408];0;1.0;1.0;1.0;1;1;3;1;
;170;arrival;N38;A9;[82482,82482,82482];[82482,82482,82482];0;1.0;1.0;1.0;1;1;2;1;
;171;departure;A6;N8;[82620,82620,82620];[82620,82620,82620];0;1.0;1.0;1.0;1;1;3;1;
;172;departure;A4;N8;[82622,82622,82622];[82622,82622,82622];0;1.0;1.0;1.0;1;1;3;1;
;173;departure;E13;N15;[82660,82660,82660];[82660,82660,82660];0;1.0;1.0;1.0;1;1;2;1;
;174;departure;E25;N15;[82667,82667,82667];[82667,82667,82667];0;1.0;1.0;1.0;1;1;2;1;
;175;departure;A15;N8;[82672,82672,82672];[82672,82672,82672];0;1.0;1.0;1.0;1;1;3;1;
;176;departure;E23;N15;[82724,82724,82724];[82724,82724,82724];0;1.0;1.0;1.0;1;1;3;1;
;177;departure;A1;N8;[82737,82737,82737];[82737,82737,82737];0;1.0;1.0;1.0;1;1;3;1;
;178;departure;E20;N15;[82740,82740,82740];[82740,82740,82740];0;1.0;1.0;1.0;1;1;3;1;
;179;departure;E12;N15;[82800,82800,82800];[82800,82800,82800];0;1.0;1.0;1.0;1;1;3;1;